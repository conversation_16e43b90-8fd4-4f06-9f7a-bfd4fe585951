import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON><PERSON>,
  CardContent,
} from "@/components/commons/base/card";
import { Badge } from "@/components/commons/badge";
import { useTranslation } from "@/hooks/useTranslation";
import { useIsDesktop } from "@/hooks/useMediaQuery";
import { CreditCardIcon } from "lucide-react";
import { getPaymentMethodIcon } from "./helpers";
import { PaymentMethodForm } from "./payment-method-form";
import { usePaymentMethods } from "@/hooks/usePaymentMethods";
import { Spinner } from "@/components/commons/loading-spinner";
import {
  PaymentMethodType,
  type CreditDebitCard,
  type TrueMoneyWallet,
  type CreditCardFormData,
  type TrueMoneyFormData,
  type AlipayFormData,
  type BasePaymentMethod,
  type AlipayAccount,
} from "@/types/payment-methods";
import type { PaymentMethodsProps } from "@components/payment-methods/types";
import type { OmiseCard } from "@/types/omise";

const getPaymentMethodDisplayText = (
  method: BasePaymentMethod,
  isDesktop: boolean,
  t: (key: string, replacements?: Record<string, string>) => string,
): string => {
  if (
    (method.type === PaymentMethodType.CREDIT_CARD ||
      method.type === PaymentMethodType.DEBIT_CARD) &&
    (method as CreditDebitCard).lastFourDigits
  ) {
    const cardMethod = method as CreditDebitCard;
    if (isDesktop) {
      return t("payment.methods.creditCardDisplay", {
        brand: cardMethod.cardBrand || "",
        lastDigits: cardMethod.lastFourDigits,
      });
    } else {
      return t("payment.methods.maskedCardNumber", {
        lastDigits: cardMethod.lastFourDigits,
      });
    }
  } else if (method.type === PaymentMethodType.TRUEMONEY) {
    const trueMoneyMethod = method as TrueMoneyWallet;
    return (
      trueMoneyMethod.displayName ||
      `TrueMoney (${trueMoneyMethod.phoneNumber})`
    );
  } else if (method.type === PaymentMethodType.ALIPAY) {
    const alipayMethod = method as AlipayAccount;
    return (
      alipayMethod.displayName ||
      `Alipay ${alipayMethod.accountEmail ? `(${alipayMethod.accountEmail})` : ""}`
    );
  }
  return method.displayName || "";
};

export const PaymentMethods: React.FC<PaymentMethodsProps> = ({
  paymentMethods: propPaymentMethods = [],
}) => {
  const { t } = useTranslation();
  const isDesktop = useIsDesktop();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedType, setSelectedType] = useState<
    PaymentMethodType | undefined
  >(undefined);
  const [selectedMethod, setSelectedMethod] = useState<
    BasePaymentMethod | undefined
  >(undefined);
  const [isEdit, setIsEdit] = useState(false);

  // Use the payment methods hook
  const {
    paymentMethods: hookPaymentMethods,
    isLoading,
    error,
    addPaymentMethod,
    getCustomerCards,
    getCustomerCard,
  } = usePaymentMethods();

  // State for Omise direct API calls
  const [customerId, setCustomerId] = useState<string>("");
  const [cardId, setCardId] = useState<string>("");
  const [omiseCards, setOmiseCards] = useState<OmiseCard[]>([]);
  const [omiseCard, setOmiseCard] = useState<OmiseCard | null>(null);
  const [isLoadingOmise, setIsLoadingOmise] = useState<boolean>(false);
  const [omiseError, setOmiseError] = useState<string | null>(null);

  // Use payment methods from props if provided, otherwise use from hook
  const displayPaymentMethods =
    propPaymentMethods.length > 0
      ? propPaymentMethods
      : hookPaymentMethods.length > 0
        ? hookPaymentMethods
        : [
            {
              id: "1",
              type: PaymentMethodType.CREDIT_CARD,
              isDefault: true,
              isActive: true,
              displayName: "Visa ****1234",
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              omiseToken: "tokn_test_123",
              lastFourDigits: "1234",
              cardBrand: "visa",
              expiryMonth: "12",
              expiryYear: "2025",
              holderName: "John Doe",
            } as CreditDebitCard,
            {
              id: "2",
              type: PaymentMethodType.TRUEMONEY,
              isDefault: false,
              isActive: true,
              displayName: "TrueMoney (***-***-1234)",
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              phoneNumber: "0891234567",
            } as TrueMoneyWallet,
          ];

  const handleEditMethod = (method: BasePaymentMethod) => {
    setSelectedMethod(method);
    setSelectedType(method.type);
    setIsEdit(true);
    setIsFormOpen(true);
  };

  const handleAddMethod = (type: PaymentMethodType) => {
    setSelectedType(type);
    setSelectedMethod(undefined);
    setIsEdit(false);
    setIsFormOpen(true);
  };

  const handleFormCancel = () => {
    setIsFormOpen(false);
    setSelectedMethod(undefined);
    setSelectedType(undefined);
  };

  const handleFormSubmit = async (
    type: PaymentMethodType,
    data: CreditCardFormData | TrueMoneyFormData | AlipayFormData,
  ) => {
    try {
      await addPaymentMethod(type, data);
      setIsFormOpen(false);
    } catch (error) {
      console.error("Error saving payment method:", error);
      // Handle error (show toast, etc.)
    }
  };

  // Handle fetching all cards for a customer from Omise
  const handleFetchCustomerCards = async () => {
    if (!customerId) {
      setOmiseError("Customer ID is required");
      return;
    }

    setIsLoadingOmise(true);
    setOmiseError(null);

    try {
      const cards = await getCustomerCards(customerId);
      setOmiseCards(cards.data || []);
    } catch (error) {
      console.error("Error fetching customer cards:", error);
      setOmiseError(`Failed to fetch customer cards: ${(error as Error).message}`);
    } finally {
      setIsLoadingOmise(false);
    }
  };

  // Handle fetching a specific card for a customer from Omise
  const handleFetchCustomerCard = async () => {
    if (!customerId) {
      setOmiseError("Customer ID is required");
      return;
    }

    if (!cardId) {
      setOmiseError("Card ID is required");
      return;
    }

    setIsLoadingOmise(true);
    setOmiseError(null);

    try {
      const card = await getCustomerCard(customerId, cardId);
      setOmiseCard(card);
    } catch (error) {
      console.error("Error fetching customer card:", error);
      setOmiseError(`Failed to fetch customer card: ${(error as Error).message}`);
    } finally {
      setIsLoadingOmise(false);
    }
  };

  if (isFormOpen && selectedType) {
    return (
      <PaymentMethodForm
        type={selectedType}
        initialData={selectedMethod}
        isEdit={isEdit}
        id={handleFormSubmit}
        onCancel={handleFormCancel}
      />
    );
  }

  return (
    <Card className="border-primary-border-card w-full sm:border-none md:border">
      <CardHeader>
        <CardTitle className="font-regular text-[23px] text-black">
          {t("payment.methods.title")}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <h3 className="mb-4 text-base font-medium">
              {t("payment.methods.current")}
            </h3>
            <div className="space-y-4">
              {isLoading ? (
                <div className="p-4 text-center">
                  <Spinner size="medium" />
                </div>
              ) : error ? (
                <div className="p-4 text-center text-red-500">
                  <p>{t("payment.methods.error")}</p>
                </div>
              ) : displayPaymentMethods.length === 0 ? (
                <div className="p-4 text-center">
                  <p>{t("payment.methods.empty")}</p>
                </div>
              ) : (
                displayPaymentMethods.map((method) => (
                  <div
                    key={method.id}
                    className="border-primary-border-card flex cursor-pointer items-center rounded-lg border p-4 lg:w-1/2"
                    onClick={() => handleEditMethod(method)}
                  >
                    <div className="mr-4 flex h-10 w-10 items-center justify-center">
                      {getPaymentMethodIcon(method, t)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <p className="font-regular">
                          {getPaymentMethodDisplayText(method, isDesktop, t)}
                        </p>
                        {method.isDefault && (
                          <Badge variant="outline" className="ml-2">
                            {t("payment.methods.main")}
                          </Badge>
                        )}
                      </div>
                      {method.type === PaymentMethodType.CREDIT_CARD && (
                        <p className="text-sm text-gray-600">
                          {`${(method as CreditDebitCard).expiryMonth}/${(method as CreditDebitCard).expiryYear.slice(-2)}`}
                        </p>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          <div>
            <h3 className="mb-4 text-base font-medium">
              {t("payment.methods.add")}
            </h3>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-1">
              <button
                className="border-primary-border-card flex items-center rounded-lg border p-4 transition-colors hover:bg-gray-50 md:w-1/2"
                onClick={() => handleAddMethod(PaymentMethodType.CREDIT_CARD)}
              >
                <div className="mr-4 flex h-10 w-10 items-center justify-center">
                  <CreditCardIcon className="h-5 w-5" />
                </div>
                <span>{t("payment.methods.addCredit")}</span>
              </button>

              <button
                className="border-primary-border-card flex items-center rounded-lg border p-4 transition-colors hover:bg-gray-50 md:w-1/2"
                onClick={() => handleAddMethod(PaymentMethodType.TRUEMONEY)}
              >
                <div className="mr-4 flex h-10 w-10 items-center justify-center">
                  <img
                    src="src/assets/images/payments/truemoney.png"
                    alt={t("payment.methods.altText.truemoney")}
                    className="h-8 w-8 object-contain"
                  />
                </div>
                <span>{t("payment.methods.types.truemoney")}</span>
              </button>

              <button
                className="border-primary-border-card flex items-center rounded-lg border p-4 transition-colors hover:bg-gray-50 md:w-1/2"
                onClick={() => handleAddMethod(PaymentMethodType.ALIPAY)}
              >
                <div className="mr-4 flex h-10 w-10 items-center justify-center">
                  <img
                    src="src/assets/images/payments/alipay.png"
                    alt={t("payment.methods.altText.alipay")}
                    className="h-5 w-5 object-contain"
                  />
                </div>
                <span>{t("payment.methods.types.alipay")}</span>
              </button>
            </div>
          </div>

          {/* Direct Omise API Access */}
          <div className="mt-8">
            <h3 className="mb-4 text-base font-medium">
              {t("payment.methods.directOmise")}
            </h3>

            <div className="space-y-4">
              <div className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2">
                <input
                  type="text"
                  value={customerId}
                  onChange={(e) => setCustomerId(e.target.value)}
                  placeholder="Customer ID"
                  className="rounded-md border border-gray-300 px-3 py-2 md:w-1/3"
                />
                <input
                  type="text"
                  value={cardId}
                  onChange={(e) => setCardId(e.target.value)}
                  placeholder="Card ID (optional)"
                  className="rounded-md border border-gray-300 px-3 py-2 md:w-1/3"
                />
                <div className="flex space-x-2">
                  <button
                    className="rounded-md bg-primary px-4 py-2 text-white"
                    onClick={handleFetchCustomerCards}
                    disabled={isLoadingOmise}
                  >
                    {isLoadingOmise ? "Loading..." : "Get All Cards"}
                  </button>
                  <button
                    className="rounded-md bg-primary px-4 py-2 text-white"
                    onClick={handleFetchCustomerCard}
                    disabled={isLoadingOmise || !cardId}
                  >
                    {isLoadingOmise ? "Loading..." : "Get Card"}
                  </button>
                </div>
              </div>

              {omiseError && (
                <div className="rounded-md bg-red-100 p-4 text-red-700">
                  {omiseError}
                </div>
              )}

              {/* Display fetched cards */}
              {omiseCards.length > 0 && (
                <div className="mt-4">
                  <h4 className="mb-2 text-sm font-medium">Customer Cards</h4>
                  <div className="space-y-2">
                    {omiseCards.map((card) => (
                      <div
                        key={card.id}
                        className="rounded-md border border-gray-200 p-4"
                      >
                        <div className="flex justify-between">
                          <div>
                            <p className="font-medium">
                              {card.brand} **** **** **** {card.last_digits}
                            </p>
                            <p className="text-sm text-gray-500">
                              Expires: {card.expiration_month}/{card.expiration_year}
                            </p>
                          </div>
                          <div>
                            {card.security_code_check ? (
                              <span className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-800">
                                Verified
                              </span>
                            ) : (
                              <span className="rounded-full bg-yellow-100 px-2 py-1 text-xs text-yellow-800">
                                Not Verified
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Display fetched card */}
              {omiseCard && (
                <div className="mt-4">
                  <h4 className="mb-2 text-sm font-medium">Card Details</h4>
                  <div className="rounded-md border border-gray-200 p-4">
                    <div className="flex justify-between">
                      <div>
                        <p className="font-medium">
                          {omiseCard.brand} **** **** **** {omiseCard.last_digits}
                        </p>
                        <p className="text-sm text-gray-500">
                          Expires: {omiseCard.expiration_month}/{omiseCard.expiration_year}
                        </p>
                        <p className="text-sm text-gray-500">
                          Name: {omiseCard.name}
                        </p>
                        <p className="text-sm text-gray-500">
                          Created: {new Date(omiseCard.created_at).toLocaleString()}
                        </p>
                      </div>
                      <div>
                        {omiseCard.security_code_check ? (
                          <span className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-800">
                            Verified
                          </span>
                        ) : (
                          <span className="rounded-full bg-yellow-100 px-2 py-1 text-xs text-yellow-800">
                            Not Verified
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
