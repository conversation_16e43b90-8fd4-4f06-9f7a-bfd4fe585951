import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

import type { Address, AddressListResponse, AddressFormData } from "@/types/address";
import { ADDRESS_ENDPOINTS } from "@/services";

import ApiService from "./apiService";

interface AddressQueryParams {
  page?: number;
  limit?: number;
}

/**
 * Hook to fetch user addresses
 */
export const useAddresses = (params?: AddressQueryParams) => {
  return useQuery({
    queryKey: [ADDRESS_ENDPOINTS.ADDRESSES, params],
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, String(value));
          }
        });
      }
      const url = queryParams.toString() 
        ? `${ADDRESS_ENDPOINTS.ADDRESSES}?${queryParams.toString()}`
        : ADDRESS_ENDPOINTS.ADDRESSES;
      return await ApiService.get<AddressListResponse>(url);
    },
    enabled: true,
  });
};

/**
 * Hook to fetch a single address by ID
 */
export const useAddress = (id: number | undefined) => {
  return useQuery({
    queryKey: ['address', id],
    queryFn: async () => {
      return await ApiService.get<Address>(ADDRESS_ENDPOINTS.ADDRESS_BY_ID(id!));
    },
    enabled: !!id,
  });
};

/**
 * Hook to create a new address
 */
export const useCreateAddress = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: AddressFormData) => {
      return await ApiService.post<Address>(ADDRESS_ENDPOINTS.ADDRESSES, data);
    },
    onSuccess: () => {
      // Invalidate and refetch addresses
      queryClient.invalidateQueries({ queryKey: [ADDRESS_ENDPOINTS.ADDRESSES] });
    },
  });
};

/**
 * Hook to update an existing address
 */
export const useUpdateAddress = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: Partial<AddressFormData> }) => {
      return await ApiService.patch<Address>(ADDRESS_ENDPOINTS.ADDRESS_BY_ID(id), data);
    },
    onSuccess: () => {
      // Invalidate and refetch addresses
      queryClient.invalidateQueries({ queryKey: [ADDRESS_ENDPOINTS.ADDRESSES] });
    },
  });
};

/**
 * Hook to delete an address
 */
export const useDeleteAddress = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: number) => {
      return await ApiService.delete(ADDRESS_ENDPOINTS.ADDRESS_BY_ID(id));
    },
    onSuccess: () => {
      // Invalidate and refetch addresses
      queryClient.invalidateQueries({ queryKey: [ADDRESS_ENDPOINTS.ADDRESSES] });
    },
  });
};

/**
 * Hook to set an address as default
 */
export const useSetDefaultAddress = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: number) => {
      return await ApiService.post<Address>(ADDRESS_ENDPOINTS.SET_DEFAULT(id));
    },
    onSuccess: () => {
      // Invalidate and refetch addresses
      queryClient.invalidateQueries({ queryKey: [ADDRESS_ENDPOINTS.ADDRESSES] });
    },
  });
};
